"use strict";

module.exports = (app) => {
  const { STRING, INTEGER } = app.Sequelize;

  const User = app.model.define(
    "user",
    {
      id: { type: INTEGER, primaryKey: true, autoIncrement: true },
      name: STRING(255),
      user: STRING(255),
      pwd: STRING(255),
      time: STRING(30),
    },
    {
      tableName: "user", // 指定表名称
      timestamps: false, // 不自动增加创建时间和更新时间
    }
  );

  // 添加分页方法
  User.paginate = async function (page = 1, pageSize = 10, where = {}, order = [["id", "desc"]]) {
    const offset = (page - 1) * pageSize;

    const result = await this.findAndCountAll({
      where,
      offset,
      limit: pageSize,
      order,
    });

    return {
      total: result.count,
      data: result.rows,
      currentPage: page,
      pageSize,
    };
  };

  return User;
};
