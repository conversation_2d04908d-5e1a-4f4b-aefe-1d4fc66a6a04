{"name": "mobile-app", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.18.0 || >=20.18.0"}, "scripts": {"dev": "vite --host", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@vant/auto-import-resolver": "^1.3.0", "@vant/touch-emulator": "^1.4.0", "axios": "^1.11.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vant": "^4.9.21", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "postcss-px-to-viewport-8-plugin": "^1.2.5", "sass": "^1.89.2", "tailwindcss": "^4.1.11", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^0.27.4", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}