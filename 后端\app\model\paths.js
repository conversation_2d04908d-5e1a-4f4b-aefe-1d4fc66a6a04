"use strict";

module.exports = (app) => {
  const { STRING, INTEGER } = app.Sequelize;

  const Paths = app.model.define(
    "paths",
    {
      id: { type: INTEGER, primaryKey: true, autoIncrement: true },
      code: STRING(255),
      title: STRING(255),
      jsons: STRING(),
      time: STRING(30),
      uid: INTEGER,
    },
    {
      tableName: "paths", // 指定表名称
      timestamps: false, // 不自动增加创建时间和更新时间
    }
  );

  // 添加分页方法
  Paths.paginate = async function (
    page = 1,
    pageSize = 10,
    where = {},
    order = [["id", "desc"]]
  ) {
    const offset = (page - 1) * pageSize;

    const result = await this.findAndCountAll({
      where,
      offset,
      limit: pageSize,
      order,
    });

    return {
      total: result.count,
      data: result.rows,
      currentPage: page,
      pageSize,
    };
  };

  return Paths;
};