const { Controller } = require("egg");
const moment = require("moment");
const { Op } = require("sequelize");

class LocationController extends Controller {
  // 新增/编辑
  async save() {
    const { ctx } = this;
    const body = ctx.request.body;
    let location = null;
    try {
      if (body.id) {
        // 修改
        const data = await ctx.model.Location.findByPk(body.id);
        if (!data) {
          ctx.status = 200;
          ctx.body = {
            code: 400,
            success: false,
            message: "id不存在",
          };
          return;
        }
        location = await data.update(body, {
          where: { id: body.id },
        });
        if (!location) {
          throw new Error("更新失败");
        }
      } else {
        // 新增
        body.time = moment().format("YYYY-MM-DD HH:mm:ss");
        location = await ctx.model.Location.create(body);
      }
    } catch (error) {
      ctx.status = 200;
      ctx.body = {
        success: false,
        message: error.message,
        code: 400,
      };
      return;
    }
    ctx.status = 200;
    ctx.body = {
      success: true,
      data: location,
      code: 200,
    };
  }

  // 获取列表
  async list() {
    const { ctx } = this;
    const body = ctx.request.body;
    const query = {};
    Object.keys(body).forEach((key) => {
      if (key != "limit" && key != "offset" && body[key] && key != "times") {
        query[key] = body[key];
      }
    });

    // 时间查询
    if (body.times && body.times.length) {
      const [startTime, endTime] = body.times.split(",");
      query.time = {
        [Op.and]: [{ [Op.gte]: startTime + " 00:00:00" }, { [Op.lte]: endTime + " 23:59:59" }],
      };
    }

    // 按时间倒序排序
    ctx.status = 200;
    ctx.body = {
      data: await ctx.model.Location.paginate(parseInt(body.offset || 1), parseInt(body.limit || 10), query, [["id", "desc"]]),
      success: true,
      code: 200,
    };
  }

  // 删除
  async delete() {
    const { ctx } = this;
    try {
      const { id } = ctx.request.body;

      if (!id) {
        throw new Error("id不能为空");
      }
      const data = await ctx.model.Location.findByPk(id);
      if (!data) {
        ctx.status = 200;
        ctx.body = {
          success: false,
          message: "id不存在",
          code: 200,
        };
        return;
      }

      await ctx.model.Location.destroy({
        where: { id: id },
      });
    } catch (error) {
      ctx.status = 200;
      ctx.body = {
        success: false,
        message: error.message,
        code: 400,
      };
      return;
    }

    ctx.status = 200;
    ctx.body = {
      success: true,
      data: null,
      code: 200,
    };
  }
}

module.exports = LocationController;
