/**
 * 网络请求方法封装
 */

import axios from "axios";
import { showDialog } from "vant";
import { useUserStore } from "@/stores/user";

// vue3环境变量
const API_URL = import.meta.env.MODE == "production" ? "http://39.108.123.71:8089/" : "api";

const request = axios.create({
  baseURL: API_URL,
  timeout: 10000,
});

interface ResType {
  code: number;
  success: boolean;
  msg: string;
  data: object | null | Array<any>;
}

declare module "axios" {
  interface AxiosResponse extends ResType {}
}

// 请求拦截
request.interceptors.request.use(
  function (config) {
    const userStore = useUserStore();
    if (userStore.userInfo?.id) {
      config.data.uid = userStore.userInfo.id;
    }
    // 序列化get请求参数
    // if (config.params && !config.data) {
    //   const searchParams = new URLSearchParams();
    //   for (const [key, value] of Object.entries(config.params)) {
    //     if (Array.isArray(value)) {
    //       value.forEach((item) => searchParams.append(key, item));
    //     } else searchParams.append(key, value as any);
    //   }
    //   const queryString = searchParams.toString();
    // }

    // // 设置请求头
    // if (config.headers) {
    //   const token = localStorage.getItem("WAuthorization");
    //   if (token) {
    //     config.headers["WAuthorization"] = "Bearer " + token;
    //   }
    // }
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

// 响应拦截
request.interceptors.response.use(
  function (response) {
    if (response.status != 200) {
      return Promise.reject(response.data);
    }
    if (response.data.code !== 200) {
      showDialog({ message: response.data.message });

      return Promise.reject(response.data);
    } else {
      return response.data;
    }
  },
  function (error) {
    return Promise.reject(error.message);
  }
);

export default request;
