/**
 * 路由拦截器
 */
import type { Router } from "vue-router";
import { useUserStore } from "@/stores/user";
import { showToast } from "vant";

// 白名单路由（不需要登录验证）
const whiteList = ["/login", "/register"];

// 设置路由拦截
export function setupRouterGuard(router: Router) {
  // 前置守卫
  router.beforeEach((to, from, next) => {
    const userStore = useUserStore();

    // 设置页面标题
    if (to.meta?.title) {
      document.title = to.meta.title as string;
    }

    // 检查是否需要登录验证
    if (to.meta?.requiresAuth !== false && !whiteList.includes(to.path)) {
      // 需要登录但未登录
      if (!userStore.userInfo) {
        showToast("请先登录");
        next({
          path: "/login",
          query: { redirect: to.fullPath },
        });
        return;
      }
    }

    // 已登录用户访问登录页，重定向到首页
    if (userStore.userInfo && (to.path === "/login" || to.path === "/register")) {
      next("/");
      return;
    }

    next();
  });

  // 后置守卫
  router.afterEach(() => {
    // 可以在这里添加页面访问统计等逻辑
  });
}
