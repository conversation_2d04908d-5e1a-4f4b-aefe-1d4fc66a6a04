// app.js 或 agent.js 文件：
const path = require('path');
class AppBootHook {
    constructor(app) {
        this.app = app;
    }

    async didLoad() {
        // 请将你的插件项目中 app.beforeStart 中的代码置于此处
        // this.app.utils = require(path.join(this.app.baseDir, 'app/utils/tool'));
    }

    async willReady() {
        // 请将你的应用项目中 app.beforeStart 中的代码置于此处
    }

    // 程序启动完成
    async serverDidReady() {
        // await this.app.utils.get_report_result(this.app);
    }
}

module.exports = AppBootHook;