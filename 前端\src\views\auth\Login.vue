<template>
  <div class="login-container">
    <!-- 顶部装饰 -->
    <div class="top-decoration"></div>

    <div class="login-content">
      <!-- Logo区域 -->
      <div class="logo-section">
        <div class="logo-icon">
          <van-icon name="location" size="48" color="#1989fa" />
        </div>
        <h1>地图规划系统</h1>
        <p>欢迎回来</p>
      </div>

      <!-- 登录表单 -->
      <div class="form-container">
        <van-form @submit="onSubmit">
          <div class="form-fields">
            <van-field v-model="formData.username" name="username" placeholder="请输入用户名"
              :rules="[{ required: true, message: '请输入用户名' }]" left-icon="user-o" size="large" clearable />
            <van-field v-model="formData.password" type="password" name="password" placeholder="请输入密码"
              :rules="[{ required: true, message: '请输入密码' }]" left-icon="lock" size="large" clearable />
          </div>


          <div class="submit-section">
            <van-button round block type="primary" native-type="submit" :loading="loading" loading-text="登录中..."
              size="large">
              登录
            </van-button>
          </div>
        </van-form>

        <!-- 注册链接 -->
        <div class="register-link flex items-center justify-end">
          <span>还没有账号？</span>
          <van-button type="primary" size="small" plain hairline @click="$router.push('/register')">
            立即注册
          </van-button>
        </div>
      </div>
    </div>

    <!-- 底部装饰 -->
    <div class="bottom-decoration"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import * as api from '@/api/index'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()


// 表单数据
const formData = reactive({
  username: '',
  password: ''
})

const loading = ref(false)

// 提交登录
const userStore = useUserStore()
const onSubmit = async () => {
  loading.value = true
  try {
    let login = await userStore.login(formData.username, formData.password)
    if (login) {
      // 跳转到原来要访问的页面或首页
      const redirect = route.query.redirect as string || '/'
      router.replace(redirect)
    }
  } catch (error) {
    console.error('登录失败:', error)
  }
  loading.value = false
}


</script>

<style scoped lang="scss">
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.top-decoration {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.bottom-decoration {
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
}

.login-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.logo-section {
  text-align: center;
  padding: 60px 0 40px;

  .logo-icon {
    margin-bottom: 16px;
    animation: bounce 2s infinite;
  }

  h1 {
    font-size: 28px;
    font-weight: bold;
    color: white;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
  }
}

.form-container {
  flex: 1;
  background: white;
  border-radius: 24px 24px 0 0;
  padding: 32px 24px 24px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.form-fields {
  margin-bottom: 20px;

  :deep(.van-field) {
    margin-bottom: 16px;
    border-radius: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;

    &:focus-within {
      border-color: #1989fa;
      box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.1);
    }
  }

  :deep(.van-field__left-icon) {
    color: #1989fa;
    margin-right: 12px;
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;

  .checkbox-text {
    font-size: 14px;
    color: #666;
  }
}

.submit-section {
  margin: 32px 0 24px;

  :deep(.van-button) {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}

.divider {
  position: relative;
  text-align: center;
  margin: 24px 0;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
  }

  span {
    background: white;
    color: #999;
    padding: 0 16px;
    font-size: 14px;
    position: relative;
    z-index: 1;
  }
}

.social-login {
  margin-bottom: 24px;

  :deep(.van-button) {
    height: 48px;
    border-color: #e9ecef;
    color: #666;

    .van-icon {
      color: #07c160;
      margin-right: 8px;
    }
  }
}

.register-link {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  span {
    color: #666;
    font-size: 14PX;
    margin-right: 8px;
  }
}

// 动画效果
@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

// PC端适配
@media (min-width: 768px) {
  .login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
  }

  .login-content {
    min-height: auto;
    max-width: 900px;
    width: 100%;
    display: flex;
    flex-direction: row;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }

  .logo-section {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 60px 40px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
      animation: float 20s infinite linear;
    }

    .logo-icon {
      position: relative;
      z-index: 1;
      margin-bottom: 24px;
    }

    h1 {
      position: relative;
      z-index: 1;
      font-size: 32px;
      margin-bottom: 16px;
    }

    p {
      position: relative;
      z-index: 1;
      font-size: 18px;
    }
  }

  .form-container {
    flex: 1;
    background: white;
    border-radius: 0;
    padding: 60px 40px;
    box-shadow: none;
  }

  .top-decoration,
  .bottom-decoration {
    display: none;
  }
}

// 大屏幕适配
@media (min-width: 1200px) {
  .login-content {
    max-width: 1000px;
  }

  .logo-section {
    padding: 80px 60px;

    h1 {
      font-size: 36px;
    }

    p {
      font-size: 20px;
    }
  }

  .form-container {
    padding: 80px 60px;
  }
}

// 平板端适配
@media (min-width: 481px) and (max-width: 767px) {
  .login-content {
    padding: 24px;
    max-width: 100%;
    margin: 0 auto;
  }

  .logo-section {
    padding: 50px 0 35px;

    h1 {
      font-size: 100px;
    }

    p {
      font-size: 60px;
    }
  }

  .form-container {
    padding: 32px 28px 28px;
  }
}

// 移动端适配
@media (max-width: 480px) {
  .login-content {
    padding: 16px;
  }

  .logo-section {
    padding: 40px 0 30px;

    h1 {
      font-size: 100px;
    }

    p {
      font-size: 60px;
    }
  }

  .form-container {
    padding: 24px 20px 20px;
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 600px) and (max-width: 767px) {
  .logo-section {
    padding: 20px 0;

    .logo-icon {
      margin-bottom: 8px;
    }

    h1 {
      font-size: 20px;
      margin-bottom: 4px;
    }

    p {
      font-size: 12px;
    }
  }

  .form-container {
    padding: 20px;
  }
}

// 动画
@keyframes float {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100px);
  }
}
</style>
