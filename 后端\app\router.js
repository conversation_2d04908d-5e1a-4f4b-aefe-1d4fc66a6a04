/**
 * @param {Egg.Application} app - egg application
 */
module.exports = (app) => {
  const { router, controller } = app;

  // 城市管理路由
  router.post("/citys/list", controller.citys.list);
  router.post("/citys/save", controller.citys.save);
  router.post("/citys/delete", controller.citys.delete);
  router.post("/citys/setdefault", controller.citys.setDefault);

  // 位置管理路由;
  router.post("/location/list", controller.location.list);
  router.post("/location/save", controller.location.save);
  router.post("/location/delete", controller.location.delete);

  // 用户管理路由;
  router.post("/user/list", controller.user.list);
  router.post("/user/save", controller.user.save);
  router.post("/user/delete", controller.user.delete);
  router.post("/user/login", controller.user.login);
  router.post("/user/register", controller.user.register);

  // 路径规划路由
  router.post("/paths/list", controller.paths.list);
  router.post("/paths/save", controller.paths.save);
  router.post("/paths/delete", controller.paths.delete);

  // 配置其他前端页面路由
  router.get("/", async (ctx) => {
    ctx.status = 404;
    ctx.body = "404 Not Found";
  });
};
