<template>
  <div class="page">
    <div class="map h-[100vh] w-[100vw]" id="map-route"></div>
    <div class="fixed top-0 left-0  h-[20px] text text-[20px]">12456</div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onBeforeUnmount } from "vue";




// dom加载完成
onMounted(() => {
  init()
});

//  初始化
let map: any = null
const init = () => {
  map = new BMapGL.Map("map-route");
  map.enableScrollWheelZoom();     //开启鼠标滚轮缩放

  map.centerAndZoom({ lng: 106.71447251732481, lat: 26.604028737423626 }, 10);
  let scaleCtrl = new BMapGL.ScaleControl();  // 添加比例尺控件
  map.addControl(scaleCtrl);
  let zoomCtrl = new BMapGL.ZoomControl();  // 添加缩放控件
  map.addControl(zoomCtrl);
}


//页面卸载
onBeforeUnmount(() => {
  if (map) {
    map.destroy();
    map = null;
  }
});

</script>

<style scoped lang="scss">
.text {
  font-size: 20px;
}
</style>
