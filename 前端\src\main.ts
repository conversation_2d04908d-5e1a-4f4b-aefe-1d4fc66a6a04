import "@/assets/main.scss";
import "@/assets/index.css";

// // 导入常用函数式组件样式
import "vant/es/dialog/style";
import "vant/es/toast/style";
import "vant/es/notify/style";

import { createApp } from "vue";
import { createPinia } from "pinia";
//持久化
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
// 引入桌面端适配
import "@vant/touch-emulator";

import App from "@/App.vue";
import router from "@/router";
import { setupRouterGuard } from "@/router/guard";

const app = createApp(App);
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

app.use(pinia);
app.use(router);

// 设置路由拦截
setupRouterGuard(router);

app.mount("#app");
