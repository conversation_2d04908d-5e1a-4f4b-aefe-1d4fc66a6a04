import request from "@/utils/request";

// 登录接口
export function login(data: { user: string; pwd: string }) {
  return request({
    url: "/user/login",
    method: "post",
    data,
  });
}

// 注册接口
export function register(data: { user: string; pwd: string; name: string }) {
  return request({
    url: "/user/register",
    method: "post",
    data,
  });
}

// 获取列表
export function userlist(data: List) {
  return request({
    url: "/user/list",
    method: "post",
    data,
  });
}

// 保存
export function usersave(data: Save) {
  return request({
    url: "/user/save",
    method: "post",
    data,
  });
}

// 删除
export function userdel(data: Delete) {
  return request({
    url: "/user/delete",
    method: "post",
    data,
  });
}
