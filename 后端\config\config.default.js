/* eslint valid-jsdoc: "off" */

/**
 * @param {Egg.EggAppInfo} appInfo app info
 */
const path = require('path');

module.exports = (appInfo) => {
  /**
   * built-in config
   * @type {Egg.EggAppConfig}
   **/
  const config = (exports = {});

  // use for cookie sign key, should change to your own and keep security
  config.keys = appInfo.name + "_1730607625195_2770";

  // add your middleware config here
  config.middleware = [];

  config.security = {
    // 关闭 CSRF 防护
    csrf: {
      enable: false,
    },
    // 跨域白名单
    domainWhiteList: ['http://127.0.0.1:8089'],
  };

  // 跨域
  config.cors = {
    origin: '*',
    allowMethods: 'GET, PUT, POST, DELETE, PATCH'
  };

  // 配置静态资源
  config.static = {
    prefix: '/', // 可以根据需要自定义路径前缀
    dir: path.join(appInfo.baseDir, 'app/public'), // 指向 Egg.js 的 public 文件夹
    // 针对 Vite 构建的 JS Module 类型
    maxAge: 31536000,
    dynamic: true,
    preload: false,
    buffer: false,
  };

  // 支持 SPA
  config.view = {
    defaultViewEngine: 'nunjucks',
    mapping: {
      '.html': 'nunjucks',
    },
  };


  // add your user config here
  const userConfig = {
    cluster: {
      listen: {
        port: 8089,
      },
    },
    sequelize: {
      dialect: "mysql", // support: mysql, mariadb, postgres, mssql
      database: "plan-map",
      host: "localhost",
      port: "3306",
      username: "root",
      password: "root",
    },

    // myAppName: 'egg',
  };

  return {
    ...config,
    ...userConfig,
  };
};
