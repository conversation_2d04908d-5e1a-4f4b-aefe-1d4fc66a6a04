import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      meta: {
        title: "首页",
        requiresAuth: true, // 是否需要登录
        roles: [], // 允许访问的角色，空数组表示所有角色都可以访问
      },
      component: () => import("@/views/home.vue"),
    },
    {
      path: "/home",
      redirect: "/",
    },
    {
      path: "/route",
      name: "route",
      meta: {
        title: "规划路线",
        requiresAuth: true,
        roles: [],
      },
      component: () => import("@/views/route.vue"),
    },
    {
      path: "/plan",
      name: "plan",
      meta: {
        title: "方案管理",
        requiresAuth: true,
        roles: [],
      },
      component: () => import("@/components/Plan.vue"),
    },
    // 登录相关路由
    {
      path: "/login",
      name: "login",
      meta: {
        title: "登录",
        requiresAuth: false,
        hideInMenu: true, // 在菜单中隐藏
      },
      component: () => import("@/views/auth/Login.vue"),
    },
    {
      path: "/register",
      name: "register",
      meta: {
        title: "注册",
        requiresAuth: false,
        hideInMenu: true,
      },
      component: () => import("@/views/auth/Register.vue"),
    },
    // 404页面
    {
      path: "/:pathMatch(.*)*",
      name: "NotFound",
      meta: {
        title: "页面不存在",
        hideInMenu: true,
      },
      component: () => import("@/views/error/404.vue"),
    },
  ],
});

export default router;
