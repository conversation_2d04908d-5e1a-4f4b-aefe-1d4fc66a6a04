# Tailwind CSS 配置指南

## 🎨 Tailwind CSS 已成功集成

本项目已经成功集成了 Tailwind CSS，并与 Vant 组件库完美兼容。

## 📦 安装的依赖

- `tailwindcss` - Tailwind CSS 核心
- `postcss` - CSS 后处理器
- `autoprefixer` - 自动添加浏览器前缀

## ⚙️ 配置文件

### 1. tailwind.config.js
- 配置了内容扫描路径
- 自定义了主题颜色（与 Vant 保持一致）
- 禁用了 `preflight` 避免与 Vant 样式冲突
- 添加了移动端友好的断点

### 2. postcss.config.js
- 集成了 Tailwind CSS
- 保留了 `postcss-px-to-viewport` 配置
- 添加了 `autoprefixer`
- 排除了 Vant 组件的 px 转换

### 3. src/assets/tailwind.css
- 引入了 Tailwind 的基础样式
- 定义了自定义工具类
- 创建了常用组件样式

## 🎯 使用示例

### 基础工具类
```vue
<template>
  <!-- 间距和布局 -->
  <div class="p-4 m-2 flex flex-col space-y-4">
    
    <!-- 颜色和字体 -->
    <h1 class="text-lg font-bold text-gray-800">标题</h1>
    <p class="text-sm text-gray-600">描述文字</p>
    
    <!-- 背景和边框 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-100">
      卡片内容
    </div>
    
    <!-- 响应式设计 -->
    <div class="text-xs sm:text-sm md:text-base lg:text-lg">
      响应式文字
    </div>
  </div>
</template>
```

### 自定义组件样式
```vue
<template>
  <!-- 使用预定义的组件样式 -->
  <div class="card p-4">
    <button class="btn-primary">主要按钮</button>
    <button class="btn-secondary">次要按钮</button>
    <input class="input-field" placeholder="输入框" />
  </div>
</template>
```

### 网格布局
```vue
<template>
  <div class="grid grid-cols-2 gap-4">
    <div class="bg-blue-100 p-3 rounded">项目 1</div>
    <div class="bg-green-100 p-3 rounded">项目 2</div>
  </div>
</template>
```

## 🔧 自定义配置

### 添加自定义颜色
在 `tailwind.config.js` 中的 `theme.extend.colors` 添加：
```js
colors: {
  brand: '#your-color',
  custom: {
    100: '#light-shade',
    500: '#medium-shade',
    900: '#dark-shade',
  }
}
```

### 添加自定义工具类
在 `src/assets/tailwind.css` 的 `@layer utilities` 中添加：
```css
@layer utilities {
  .your-custom-class {
    /* 自定义样式 */
  }
}
```

## 📱 移动端适配

### 安全区域适配
```vue
<template>
  <div class="safe-area-top safe-area-bottom">
    <!-- 内容会自动适配安全区域 -->
  </div>
</template>
```

### 响应式断点
- `xs`: 375px (手机竖屏)
- `sm`: 640px (大手机)
- `md`: 768px (平板)
- `lg`: 1024px (桌面)
- `xl`: 1280px (大桌面)

## 🤝 与 Vant 的兼容性

1. **样式隔离**: 禁用了 Tailwind 的 `preflight` 避免冲突
2. **px 转换**: Vant 组件不会被 `postcss-px-to-viewport` 影响
3. **颜色统一**: Tailwind 自定义颜色与 Vant 主题色保持一致

## 💡 最佳实践

1. **优先使用 Tailwind**: 对于布局、间距、颜色等使用 Tailwind
2. **Vant 处理交互**: 使用 Vant 组件处理复杂的移动端交互
3. **自定义组件**: 将常用的样式组合封装为组件类
4. **响应式优先**: 始终考虑不同屏幕尺寸的适配

## 🚀 开发建议

- 使用 Tailwind CSS IntelliSense 插件获得更好的开发体验
- 定期清理未使用的样式类
- 合理使用 `@apply` 指令封装重复的样式组合
- 保持 Tailwind 和 Vant 样式的平衡，避免过度混合
