const axios = require('axios');
const moment = require('moment');
module.exports = {
    async get_report_result(ctx) {

        let now_data = moment().format('YYYY-MM-DD');
        // 获取前三天的日期
        let three_days_ago = moment().subtract(3, 'days').format('YYYY-MM-DD');
        const sql = `
            SELECT * FROM questions
            WHERE time BETWEEN ? AND ?
            `;

        //查询数据
        const res = await ctx.model.query(sql, {
            replacements: [three_days_ago + ' 00:00:00', now_data + ' 23:59:59'],
            type: ctx.model.Sequelize.QueryTypes.SELECT
        });

        let title = `${three_days_ago}至${now_data}`;
        let data = [
            {
                "type": "已解决",
                "value": 0
            },
            {
                "type": "待解决",
                "value": 0
            }
        ]
        if (res.length > 0) {
            res.forEach(item => {
                if (item.status == '待解决') {
                    data[1].value += 1;
                } else {
                    data[0].value += 1;
                }
            });
        }

        let msg = {
            "msg_type": "interactive",
            "card": {
                "type": "template",
                "data": {
                    "template_id": "AAqDWuGzanlwr",
                    "template_version_name": "1.0.3",
                    "template_variable": {
                        "title": title,
                        "url": `http://39.108.123.71:8089/index.html#/list?time=${three_days_ago},${now_data}`,
                        "result": {
                            "type": "bar",
                            "title": {
                                "text": `问题统计,共${res.length}个`,
                            },
                            "data": {
                                "values": data
                            },
                            "xField": "type",
                            "yField": "value",
                            "axes": [{ "orient": "left", "tick": { "noDecimals": true } }],
                            "seriesField": "type",
                            "legend": {
                                "visible": true,
                                "orient": "bottom"
                            }
                        }
                    }
                }
            }
        }

        // 正式
        // const msg_result = await axios.post('https://open.feishu.cn/open-apis/bot/v2/hook/a272531b-05a2-48bf-a2f9-b24738060fb3', msg);


        // 测试
        const msg_result = await axios.post('https://open.feishu.cn/open-apis/bot/v2/hook/f516ae5a-05dd-4b18-a4e5-f367da733611', msg);

    }
}

