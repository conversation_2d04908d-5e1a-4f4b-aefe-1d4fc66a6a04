/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Alan: typeof import('./src/components/Alan.vue')['default']
    APlan: typeof import('./src/components/APlan.vue')['default']
    copy: typeof import('./src/components/Plan copy.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    IconCommunity: typeof import('./src/components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./src/components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./src/components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./src/components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./src/components/icons/IconTooling.vue')['default']
    MarkerManager: typeof import('./src/components/MarkerManager.vue')['default']
    Plan: typeof import('./src/components/Plan.vue')['default']
    ResponsiveExample: typeof import('./src/components/ResponsiveExample.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TheWelcome: typeof import('./src/components/TheWelcome.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCard: typeof import('vant/es')['Card']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanDialog: typeof import('vant/es')['Dialog']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanPopup: typeof import('vant/es')['Popup']
    VanSearch: typeof import('vant/es')['Search']
    VanTag: typeof import('vant/es')['Tag']
    WelcomeItem: typeof import('./src/components/WelcomeItem.vue')['default']
  }
}
