<template>
  <div>
    <!-- 标点管理按钮 -->
    <van-button @click="showPlanPopup = true" type="warning" icon="guide-o">
      路径规划
    </van-button>

    <!-- 路径规划列表 -->
    <van-popup v-model:show="showPlanPopup" position="bottom" :style="{ height: '80%' }">
      <van-nav-bar :title="'路径规划 ' + plansList.length" @click-right="showCreateDialog = true">
        <template #right>
          <van-button type="primary" size="small">新建路径规划</van-button>
        </template>
      </van-nav-bar>
      <div class="plans-list">
        <van-empty v-if="!plansList.length" description="暂无路径规划" />
        <div v-else class="plan-items">
          <div v-for="plan in plansList" :key="plan.id" class="plan-item" @click="viewPlanDetail(plan)">
            <div class="plan-header">
              <h3 class="plan-title">{{ plan.title }}</h3>
              <div class="plan-actions">
                <van-button type="primary" size="mini" @click.stop="editPlan(plan)">
                  编辑
                </van-button>
                <van-button type="danger" size="mini" @click.stop="deletePlan(plan.id!)">
                  删除
                </van-button>
              </div>
            </div>
            <div class="plan-info">
              <span class="plan-code">编号: {{ plan.code }}</span>
              <span class="plan-time">{{ plan.time }}</span>
            </div>
            <div class="plan-locations">
              <span class="locations-count">
                包含 {{ getPlanLocationsCount(plan.jsons) }} 个地点
              </span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 创建/编辑路径规划弹窗 -->
    <van-dialog v-model:show="showCreateDialog" :title="editingPlan ? '编辑路径规划' : '新建路径规划'" show-cancel-button
      @confirm="savePlan" @cancel="resetForm" :close-on-click-overlay="false" class="plan-dialog">
      <div class="form-content">
        <!-- 路径名称 -->
        <van-field v-model="formData.title" label="路径名称" placeholder="请输入路径名称" required />

        <!-- 地点选择区域 -->
        <div class="locations-section">
          <div class="section-title">选择地点并排序</div>

          <!-- 可选地点列表 -->
          <div class="available-locations">
            <div class="subtitle">可选地点:</div>
            <div class="location-chips">
              <van-tag v-for="location in availableLocations" :key="location.id"
                :type="selectedLocationIds.includes(location.id) ? 'primary' : 'default'"
                @click="toggleLocation(location)" class="location-chip">
                {{ location.name }}
              </van-tag>
            </div>
          </div>

          <!-- 已选地点排序 -->
          <div class="selected-locations" v-if="selectedLocations.length">
            <div class="subtitle">路径顺序 (点击按钮调整顺序):</div>
            <div class="sorted-locations">
              <div v-for="(location, index) in selectedLocations" :key="location.id" class="sorted-location-item">
                <span class="location-order">{{ index + 1 }}</span>
                <span class="location-name">{{ location.name }}</span>
                <div class="location-actions">
                  <van-button v-if="index > 0" type="primary" size="mini" icon="arrow-up" @click="moveLocationUp(index)"
                    class="move-btn" />
                  <van-button v-if="index < selectedLocations.length - 1" type="primary" size="mini" icon="arrow-down"
                    @click="moveLocationDown(index)" class="move-btn" />
                  <van-button type="danger" size="mini" icon="cross" @click="removeLocation(index)"
                    class="remove-btn" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 路径详情弹窗 -->
    <van-dialog v-model:show="showDetailDialog" :title="currentPlan?.title || '路径详情'" show-cancel-button
      confirm-button-text="关闭" :show-confirm-button="false" cancel-button-text="关闭" class="detail-dialog">
      <div class="detail-content" v-if="currentPlan">
        <div class="detail-info">
          <p><strong>编号:</strong> {{ currentPlan.code }}</p>
          <p><strong>创建时间:</strong> {{ currentPlan.time }}</p>
        </div>
        <div class="detail-locations">
          <h4>路径地点:</h4>
          <div class="location-list">
            <div v-for="(location, index) in getPlanLocations(currentPlan.jsons)" :key="location.id"
              class="location-detail-item">
              <span class="location-index">{{ index + 1 }}</span>
              <div class="location-detail">
                <div class="location-name">{{ location.name }}</div>
                <div class="location-address">{{ location.address }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { showToast, showConfirmDialog } from "vant";
import { locationlist } from "@/api/locations";
import { pathslist, pathssave, pathsdel } from "@/api/paths";

// 接口类型定义
interface Location {
  id: number;
  name: string;
  description: string;
  address: string;
  lng: string;
  lat: string;
  time: string;
  uid: number;
}

interface Plan {
  id?: number;
  code: string;
  title: string;
  jsons: string;
  time: string;
  uid: number;
}

// 响应式数据
const plansList = ref<Plan[]>([]);
const availableLocations = ref<Location[]>([]);
const selectedLocations = ref<Location[]>([]);
const selectedLocationIds = computed(() => selectedLocations.value.map(loc => loc.id));

// 弹窗控制
const showPlanPopup = ref(false);
const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const editingPlan = ref<Plan | null>(null);
const currentPlan = ref<Plan | null>(null);


// 表单数据
const formData = ref({
  title: "",
});

// 生成随机编号
const generateCode = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 7);
  return `${timestamp}_${random}`.toUpperCase();
};

// 获取路径规划列表
const loadPlansList = async () => {
  try {
    const response = await pathslist({
      limit: 100,
      offset: 1,
    });
    if (response.success) {
      plansList.value = response.data.data || [];
    }
  } catch (error) {
    showToast("获取路径规划列表失败");
  }
};

// 获取可用地点列表
const loadAvailableLocations = async () => {
  try {
    const response = await locationlist({
      limit: 100,
      offset: 1,
    });
    if (response.success) {
      availableLocations.value = response.data.data || [];
    }
  } catch (error) {
    showToast("获取地点列表失败");
  }
};

// 切换地点选择
const toggleLocation = (location: Location) => {
  const index = selectedLocations.value.findIndex(loc => loc.id === location.id);
  if (index > -1) {
    selectedLocations.value.splice(index, 1);
  } else {
    selectedLocations.value.push(location);
  }
};

// 移除已选地点
const removeLocation = (index: number) => {
  selectedLocations.value.splice(index, 1);
};

// 向上移动地点
const moveLocationUp = (index: number) => {
  if (index > 0) {
    const temp = selectedLocations.value[index];
    selectedLocations.value[index] = selectedLocations.value[index - 1];
    selectedLocations.value[index - 1] = temp;
  }
};

// 向下移动地点
const moveLocationDown = (index: number) => {
  if (index < selectedLocations.value.length - 1) {
    const temp = selectedLocations.value[index];
    selectedLocations.value[index] = selectedLocations.value[index + 1];
    selectedLocations.value[index + 1] = temp;
  }
};

// 获取路径中的地点数量
const getPlanLocationsCount = (jsons: string): number => {
  try {
    const locations = JSON.parse(jsons || "[]");
    return Array.isArray(locations) ? locations.length : 0;
  } catch {
    return 0;
  }
};

// 获取路径中的地点列表
const getPlanLocations = (jsons: string): Location[] => {
  try {
    const locations = JSON.parse(jsons || "[]");
    return Array.isArray(locations) ? locations : [];
  } catch {
    return [];
  }
};

// 保存路径规划
const savePlan = async () => {
  if (!formData.value.title.trim()) {
    showToast("请输入路径名称");
    return;
  }

  if (selectedLocations.value.length === 0) {
    showToast("请至少选择一个地点");
    return;
  }

  try {
    const planData = {
      title: formData.value.title,
      code: editingPlan.value?.code || generateCode(),
      jsons: JSON.stringify(selectedLocations.value),
      ...(editingPlan.value?.id && { id: editingPlan.value.id }),
    };

    const response = await pathssave(planData);
    if (response.success) {
      showToast(editingPlan.value ? "更新成功" : "创建成功");
      showCreateDialog.value = false;
      resetForm();
      loadPlansList();
    } else {
      showToast("保存失败");
    }
  } catch (error) {
    showToast("保存失败");
  }
};

// 编辑路径规划
const editPlan = (plan: Plan) => {
  editingPlan.value = plan;
  formData.value.title = plan.title;
  selectedLocations.value = getPlanLocations(plan.jsons);
  showCreateDialog.value = true;
};

// 删除路径规划
const deletePlan = async (id: number) => {
  try {
    await showConfirmDialog({
      title: "确认删除",
      message: "确定要删除这个路径规划吗？",
    });

    const response = await pathsdel({ id });
    if (response.success) {
      showToast("删除成功");
      loadPlansList();
    } else {
      showToast("删除失败");
    }
  } catch (error) {
    // 用户取消删除
  }
};

// 查看路径详情
const viewPlanDetail = (plan: Plan) => {
  currentPlan.value = plan;
  showDetailDialog.value = true;
};

// 重置表单
const resetForm = () => {
  formData.value.title = "";
  selectedLocations.value = [];
  editingPlan.value = null;
};

// 页面加载完成
onMounted(() => {
  loadPlansList();
  loadAvailableLocations();
});

// 页面卸载
onBeforeUnmount(() => {
  // 清理工作
});
</script>

<style scoped lang="scss">
/* ===== 基础样式 ===== */
.plan-container {
  // background-color: #f5f5f5;
  // min-height: 100vh;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
}

.plans-list {
  .plan-items {
    .plan-item {
      background: white;
      border-radius: 8px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .plan-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .plan-title {
          margin: 0;
          font-weight: 600;
          color: #333;
        }

        .plan-actions {
          display: flex;
          gap: 8px;
        }
      }

      .plan-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #666;

        .plan-code {
          font-family: monospace;
          background: #f0f0f0;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }

      .plan-locations {
        color: #999;

        .locations-count {
          background: #e8f4fd;
          color: #1989fa;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }
  }
}

/* ===== 弹窗样式 ===== */
.plan-dialog {
  .form-content {
    .locations-section {
      .section-title {
        font-weight: 600;
        color: #333;
      }

      .subtitle {
        color: #666;
      }

      .available-locations {
        .location-chips {
          display: flex;
          flex-wrap: wrap;

          .location-chip {
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              transform: scale(1.05);
            }
          }
        }
      }

      .selected-locations {
        .sorted-locations {
          .sorted-location-item {
            display: flex;
            align-items: center;
            background: #f0f8ff;
            border: 1px solid #1989fa;
            border-radius: 6px;
            transition: all 0.2s ease;

            &:hover {
              background: #e8f4fd;
            }

            .location-order {
              background: #1989fa;
              color: white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
            }

            .location-name {
              flex: 1;
            }

            .location-actions {
              display: flex;
              align-items: center;

              .remove-btn {
                background-color: #ff4444;
                border-color: #ff4444;
              }
            }
          }
        }
      }
    }
  }
}

.plan-popup {
  height: 100%;
  display: flex;
  flex-direction: column;

  .plan-content {
    flex: 1;
    overflow: hidden;
    background-color: #f5f5f5;
  }
}

.detail-dialog {
  .detail-content {
    .detail-info {
      p {
        margin: 8px 0;
        color: #333;
      }
    }

    .detail-locations {
      h4 {
        margin: 0 0 12px 0;
        color: #333;
      }

      .location-list {
        .location-detail-item {
          display: flex;
          align-items: center;
          background: #f8f9fa;
          border-radius: 6px;

          .location-index {
            background: #1989fa;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
          }

          .location-detail {
            flex: 1;

            .location-name {
              font-weight: 600;
              color: #333;
            }

            .location-address {
              color: #666;
            }
          }
        }
      }
    }
  }
}

/* ===== PC端样式 (min-width: 769px) ===== */
@media (min-width: 769px) {
  .plan-container {
    padding: 20px;
  }

  .header-actions {
    margin-bottom: 16px;
  }

  .plans-list {
    .plan-items {
      .plan-item {
        padding: 20px;

        .plan-header {
          margin-bottom: 12px;

          .plan-title {
            font-size: 18px;
          }

          .plan-actions {
            .van-button {
              padding: 6px 12px;
              font-size: 14px;
            }
          }
        }

        .plan-info {
          margin-bottom: 8px;
          font-size: 14px;

          .plan-code {
            font-size: 12px;
          }
        }

        .plan-locations {
          font-size: 13px;
        }
      }
    }
  }

  ::v-deep(.van-dialog) {
    width: 500px
  }

  .plan-dialog {
    .form-content {
      padding: 20px;

      .locations-section {
        margin-top: 16px;

        .section-title {
          font-size: 16px;
          margin-bottom: 12px;
        }

        .subtitle {
          font-size: 14px;
          margin-bottom: 8px;
        }

        .available-locations {
          margin-bottom: 16px;

          .location-chips {
            gap: 8px;

            .location-chip {
              border-radius: 6px;
              font-size: 14px;
              padding: 6px 12px;
            }
          }
        }

        .selected-locations {
          .sorted-locations {
            .sorted-location-item {
              padding: 12px 16px;
              margin-bottom: 8px;

              .location-order {
                width: 24px;
                height: 24px;
                font-size: 12px;
                margin-right: 12px;
              }

              .location-name {
                font-size: 16px;
                margin-right: 12px;
              }

              .location-actions {
                gap: 6px;

                .move-btn,
                .remove-btn {
                  min-width: 28px;
                  height: 28px;
                  padding: 4px 6px;

                  :deep(.van-button__text) {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .detail-dialog {
    .detail-content {
      padding: 20px;

      .detail-info {
        margin-bottom: 16px;

        p {
          font-size: 16px;
        }
      }

      .detail-locations {
        height: 500px;
        overflow-y: scroll;

        h4 {
          font-size: 16px;
        }

        .location-list {
          .location-detail-item {
            padding: 16px;
            margin-bottom: 8px;

            .location-index {
              width: 28px;
              height: 28px;
              font-size: 14px;
              margin-right: 16px;
            }

            .location-detail {
              .location-name {
                font-size: 16px;
                margin-bottom: 4px;
              }

              .location-address {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}

/* ===== 移动端样式 (max-width: 768px) ===== */
@media (max-width: 768px) {
  .plan-container {
    padding: 48px;
  }

  .header-actions {
    margin-bottom: 48px;
  }

  .plans-list {
    .plan-items {
      .plan-item {
        padding: 64px 48px;

        .plan-header {
          // flex-direction: column;
          align-items: flex-start;
          gap: 48px;
          margin-bottom: 48px;

          .plan-title {
            font-size: 80px;
          }

          .plan-actions {
            align-self: flex-end;

            .van-button {
              font-size: 12px;
            }
          }
        }

        .plan-info {
          margin-bottom: 32px;
          font-size: 64px;

          .plan-code {
            font-size: 60px;
            border-radius: 15px;
            padding: 16px 32px;
          }

        }

        .plan-locations {
          font-size: 60px;

          .locations-count {
            padding: 16px 32px;
            border-radius: 15px;
          }
        }
      }
    }
  }

  .plan-dialog {
    .form-content {
      padding: 64px;

      .locations-section {
        margin-top: 64px;

        .section-title {
          font-size: 72px;
          margin-bottom: 48px;
        }

        .subtitle {
          font-size: 64px;
          margin-bottom: 48px;
        }

        .available-locations {
          margin-bottom: 80px;

          .location-chips {
            height: 700px;
            overflow-y: scroll;
            gap: 48px;

            .location-chip {
              font-size: 65px;
              padding: 30px 50px;
            }
          }
        }

        .selected-locations {
          .sorted-locations {
            height: 800px;
            overflow-y: scroll;

            .sorted-location-item {
              padding: 30px 50px;
              margin-bottom: 48px;
              border-radius: 30px;

              .location-order {
                width: 100px;
                height: 100px;
                font-size: 50px;
                margin-right: 64px;
              }

              .location-name {
                font-size: 70px;
                margin-right: 64px;
              }

              .location-actions {
                gap: 6px;

                .move-btn,
                .remove-btn {
                  min-width: 100px;
                  height: 100px;
                  padding: 4px 6px;

                  :deep(.van-button__text) {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .detail-dialog {
    .detail-content {
      padding: 64px;

      .detail-info {
        margin-bottom: 64px;

        p {
          font-size: 64px;
        }
      }

      .detail-locations {
        height: 2000px;
        overflow-y: scroll;

        h4 {
          font-size: 64px;
        }

        .location-list {
          .location-detail-item {
            padding: 64px 48px;
            margin-bottom: 48px;

            .location-index {
              width: 128px;
              height: 128px;
              font-size: 64px;
              margin-right: 64px;
            }

            .location-detail {
              .location-name {
                font-size: 72px;
                margin-bottom: 24px;
              }

              .location-address {
                font-size: 64px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
