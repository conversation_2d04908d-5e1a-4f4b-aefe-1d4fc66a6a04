"use strict";

module.exports = (app) => {
  const { STRING, INTEGER } = app.Sequelize;

  const Citys = app.model.define(
    "citys",
    {
      id: { type: INTEGER, primaryKey: true, autoIncrement: true },
      province: STRING(255),
      city: STRING(255),
      district: STRING(255),
      lng: STRING(255),
      lat: STRING(255),
      active: STRING(255),
      time: STRING(30),
      uid: INTEGER,
    },
    {
      tableName: "citys", // 指定表名称
      timestamps: false, // 不自动增加创建时间和更新时间
    }
  );

  // 添加分页方法
  Citys.paginate = async function (
    page = 1,
    pageSize = 10,
    where = {},
    order = [["id", "desc"]]
  ) {
    const offset = (page - 1) * pageSize;

    const result = await this.findAndCountAll({
      where,
      offset,
      limit: pageSize,
      order,
    });

    return {
      total: result.count,
      data: result.rows,
      currentPage: page,
      pageSize,
    };
  };

  return Citys;
};