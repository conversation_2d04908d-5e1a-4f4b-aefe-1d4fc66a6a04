const { Controller } = require("egg");
const moment = require("moment");
const { Op } = require("sequelize");

class UserController extends Controller {
  // 注册
  async register() {
    const { ctx } = this;
    const body = ctx.request.body;
    try {
      // 判读是否有空值
      if (!body.name) {
        throw new Error("名称不能为空");
      }
      if (!body.user) {
        throw new Error("账号不能为空");
      }
      if (!body.pwd) {
        throw new Error("密码不能为空");
      }
      // 校验账号是否重复
      let user = await ctx.model.User.findOne({
        where: {
          user: body.user,
        },
      });
      if (user) {
        throw new Error("账号已存在");
      }
      // 插入时间
      body.time = moment().format("YYYY-MM-DD HH:mm:ss");
      user = await ctx.model.User.create(body);
      ctx.status = 200;
      ctx.body = {
        success: true,
        data: user,
        code: 200,
      };
    } catch (error) {
      ctx.status = 200;
      ctx.body = {
        success: false,
        message: error.message,
        code: 400,
      };
      return;
    }
  }

  // 登录
  async login() {
    const { ctx } = this;
    const body = ctx.request.body;
    try {
      if (!body.user) {
        throw new Error("账号不能为空");
      }
      if (!body.pwd) {
        throw new Error("密码不能为空");
      }
      // 只查询账号和名称和id
      const user = await ctx.model.User.findOne({
        where: {
          user: body.user,
          pwd: body.pwd,
        },
        attributes: ["id", "name", "user"],
      });
      if (!user) {
        throw new Error("账号或密码错误");
      }
      ctx.status = 200;
      ctx.body = {
        success: true,
        data: user,
        code: 200,
      };
    } catch (error) {
      ctx.status = 200;
      ctx.body = {
        success: false,
        message: error.message,
        code: 400,
      };
      return;
    }
  }

  // 新增/编辑
  async save() {
    const { ctx } = this;
    const body = ctx.request.body;
    let user = null;
    try {
      if (body.id) {
        // 修改
        const data = await ctx.model.User.findByPk(body.id);
        if (!data) {
          ctx.status = 200;
          ctx.body = {
            code: 400,
            success: false,
            message: "id不存在",
          };
          return;
        }
        user = await data.update(body, {
          where: { id: body.id },
        });
        if (!user) {
          throw new Error("更新失败");
        }
      } else {
        // 新增
        body.time = moment().format("YYYY-MM-DD HH:mm:ss");
        user = await ctx.model.User.create(body);
      }
    } catch (error) {
      ctx.status = 200;
      ctx.body = {
        success: false,
        message: error.message,
        code: 400,
      };
      return;
    }
    ctx.status = 200;
    ctx.body = {
      success: true,
      data: user,
      code: 200,
    };
  }

  // 获取列表
  async list() {
    const { ctx } = this;
    const body = ctx.request.body;
    const query = {};
    Object.keys(body).forEach((key) => {
      if (key != "limit" && key != "offset" && body[key] && key != "times") {
        query[key] = body[key];
      }
    });

    // 时间查询
    if (body.times && body.times.length) {
      const [startTime, endTime] = body.times.split(",");
      query.time = {
        [Op.and]: [{ [Op.gte]: startTime + " 00:00:00" }, { [Op.lte]: endTime + " 23:59:59" }],
      };
    }
    ctx.status = 200;
    ctx.body = {
      data: await ctx.model.User.paginate(parseInt(body.offset || 1), parseInt(body.limit || 10), query),
      success: true,
      code: 200,
    };
  }

  // 删除
  async delete() {
    const { ctx } = this;
    try {
      const { id } = ctx.request.body;

      if (!id) {
        throw new Error("id不能为空");
      }
      const data = await ctx.model.User.findByPk(id);
      if (!data) {
        ctx.status = 200;
        ctx.body = {
          success: false,
          message: "id不存在",
          code: 200,
        };
        return;
      }

      await ctx.model.User.destroy({
        where: { id: id },
      });
    } catch (error) {
      ctx.status = 200;
      ctx.body = {
        success: false,
        message: error.message,
        code: 400,
      };
      return;
    }

    ctx.status = 200;
    ctx.body = {
      success: true,
      data: null,
      code: 200,
    };
  }
}

module.exports = UserController;
