<template>
  <div class="error-container">
    <!-- 顶部装饰 -->
    <div class="top-decoration"></div>

    <div class="error-content">
      <!-- 错误图标区域 -->
      <div class="error-visual">
        <div class="error-icon">
          <van-icon name="warning-o" :size="iconSize" color="#ff6b6b" />
        </div>
        <div class="floating-elements">
          <div class="floating-dot dot-1"></div>
          <div class="floating-dot dot-2"></div>
          <div class="floating-dot dot-3"></div>
        </div>
      </div>

      <!-- 错误信息区域 -->
      <div class="error-main">
        <div class="error-info">
          <h1 class="error-code">404</h1>
          <h2 class="error-title">页面不存在</h2>
          <p class="error-desc">抱歉，您访问的页面不存在或已被删除</p>
        </div>

        <div class="error-actions">
          <van-button type="primary" round :size="buttonSize" @click="goHome" class="action-button primary-button"
            style="margin-bottom:10px">
            <van-icon name="home-o" />
            返回首页
          </van-button>
          <van-button plain round :size="buttonSize" @click="goBack" class="action-button secondary-button">
            <van-icon name="arrow-left" />
            返回上页
          </van-button>
        </div>

        <div class="error-suggestions">
          <h3>您可以尝试：</h3>
          <ul>
            <li>
              <van-icon name="search" />
              <span>检查网址是否正确</span>
            </li>
            <li>
              <van-icon name="home-o" />
              <span>返回首页重新导航</span>
            </li>
            <li>
              <van-icon name="service-o" />
              <span>联系管理员获取帮助</span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 底部装饰 -->
    <div class="bottom-decoration"></div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 窗口宽度响应式检测
const windowWidth = ref(window.innerWidth)

const updateWindowWidth = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', updateWindowWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateWindowWidth)
})

// 设备类型检测
const deviceType = computed(() => {
  if (windowWidth.value <= 480) return 'mobile'
  if (windowWidth.value <= 768) return 'tablet'
  return 'desktop'
})

// 响应式图标大小
const iconSize = computed(() => {
  switch (deviceType.value) {
    case 'mobile': return 100
    case 'tablet': return 70
    case 'desktop': return 80
    default: return 70
  }
})

// 响应式按钮大小
const buttonSize = computed(() => {
  switch (deviceType.value) {
    case 'mobile': return 'normal'
    case 'tablet': return 'large'
    case 'desktop': return 'large'
    default: return 'normal'
  }
})

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped lang="scss">
@media (min-width: 768px) {
  .error-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
  }

  .error-content {
    text-align: center;
    max-width: 500px;
    width: 100%;
    background: white;
    border-radius: 16px;
    padding: 40px 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .error-image {
    margin-bottom: 24px;
  }

  .error-info {
    margin-bottom: 32px;

    h1 {
      font-size: 72px;
      font-weight: bold;
      color: #ff6b6b;
      margin: 0 0 16px 0;
      line-height: 1;
    }

    h2 {
      font-size: 24px;
      color: #333;
      margin: 0 0 16px 0;
    }

    p {
      color: #666;
      font-size: 16px;
      line-height: 1.5;
      margin: 0;
    }
  }

  .error-actions {
    margin-bottom: 32px;
  }

  .error-suggestions {
    text-align: left;

    h3 {
      font-size: 16px;
      color: #333;
      margin-bottom: 12px;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        color: #666;
        font-size: 14px;
        line-height: 1.8;
        position: relative;
        padding-left: 20px;

        &::before {
          content: '•';
          color: #1989fa;
          position: absolute;
          left: 0;
          font-weight: bold;
        }
      }
    }
  }
}

// 平板端适配
@media (min-width: 481px) and (max-width: 767px) {
  .error-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
  }

  .error-content {
    text-align: center;
    max-width: 1000px;
    width: 100%;
    background: white;
    border-radius: 16px;
    padding: 40px 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .error-image {
    margin-bottom: 24px;
  }

  .error-info {
    margin-bottom: 32px;

    h1 {
      font-size: 100px;
      font-weight: bold;
      color: #ff6b6b;
      margin: 0 0 16px 0;

      line-height: 1;
    }

    h2 {
      font-size: 40px;
      color: #333;
      margin: 0 0 16px 0;
    }

    p {
      color: #666;
      font-size: 30px;
      line-height: 1.5;
      margin: 0;
    }
  }

  .error-actions {
    margin-bottom: 32px;
  }

  .error-suggestions {
    text-align: left;

    h3 {
      font-size: 35px;
      color: #333;
      margin-bottom: 12px;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        color: #666;
        font-size: 30px;
        line-height: 1.8;
        position: relative;
        padding-left: 20px;

        &::before {
          content: '•';
          color: #1989fa;
          position: absolute;
          left: 0;
          font-weight: bold;
        }
      }
    }
  }
}


// 移动端适配
@media (max-width: 480px) {
  .error-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
  }

  .error-content {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    max-width: 95%;
    height: 70vh;
    width: 100%;
    background: white;
    border-radius: 16px;
    padding: 40px 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .error-image {
    margin-bottom: 24px;
  }

  .error-info {
    margin-bottom: 200px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;

    h1 {
      font-size: 120px;
      font-weight: bold;
      color: #ff6b6b;
      margin: 0 0 16px 0;

      line-height: 1;
    }

    h2 {
      font-size: 80px;
      color: #333;
      margin: 0 0 16px 0;
    }

    p {
      color: #666;
      font-size: 60px;
      line-height: 1.5;
      margin: 0;
    }
  }

  .error-actions {
    margin-bottom: 32px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
  }

  .error-suggestions {
    display: none;
  }
}

// @media (max-width: 768px) {
//   .error-content {
//     padding: 32px 24px;
//   }

//   .error-info h1 {
//     font-size: 56px;
//   }

//   .error-info h2 {
//     font-size: 20px;
//   }

//   .error-actions {
//     .van-button {
//       margin: 8px 0 !important;
//       display: block;
//       width: 100%;
//     }
//   }
// }</style>
