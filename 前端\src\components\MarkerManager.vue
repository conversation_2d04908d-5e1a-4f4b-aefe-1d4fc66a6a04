<template>
  <div>
    <!-- 标点管理按钮 -->
    <van-button @click="showMarkerList" icon="edit" color="#FFA61B">
      标点管理
    </van-button>

    <!-- 标点列表弹窗 -->
    <van-popup v-model:show="showMarkerListPopup" position="bottom" :style="{ height: '70%' }">
      <div class="marker-list-popup">
        <van-nav-bar :title="'标点管理 ' + markers.length" @click-right="addNewMarker">
          <template #right>
            <van-button type="primary" size="small">添加标点</van-button>
          </template>
        </van-nav-bar>

        <div class="marker-list-content">
          <van-empty v-if="markers.length === 0" description="暂无标点" />

          <van-cell-group v-else>
            <van-cell v-for="(marker, index) in markers" :key="index" :title="marker.name || '未命名标点'"
              :label="marker.address" clickable @click="editMarker(index)">
              <template #value>
                <div class="marker-actions">
                  <van-button type="primary" size="mini" plain @click.stop="locateToMarker(marker)">
                    定位
                  </van-button>
                  <van-button type="danger" size="mini" plain @click.stop="confirmDeleteMarker(index)">
                    删除
                  </van-button>
                </div>
              </template>
              <template #right-icon>
                <van-icon name="edit" />
              </template>
            </van-cell>
          </van-cell-group>
        </div>

        <div class="p-4">
          <van-button type="default" block @click="closeMarkerList">关闭</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 标点信息编辑弹窗 -->
    <van-popup v-model:show="showMarkerPopup" position="center" :style="{ width: '90%' }">
      <div class="marker-popup">
        <van-nav-bar :title="isEditingMarker ? '编辑标点' : '添加标点'" />

        <div class="p-4">
          <van-field v-model="currentMarker.name" label="名称" placeholder="请输入标点名称" required />

          <van-field v-model="currentMarker.description" label="描述" placeholder="请输入标点描述" type="textarea" rows="3" />

          <van-field v-model="currentMarker.address" label="地址" placeholder="地址信息" readonly />

          <div class="mt-4 flex gap-2">
            <van-button @click="closeMarkerPopup" block>取消</van-button>
            <van-button type="primary" @click="saveMarker" block>保存</van-button>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import * as api from '@/api/index'
import { showConfirmDialog } from 'vant';

// 接口定义
interface MARKERS {
  name: string,
  description: string,
  address: string,
  lng: number,
  lat: number,
  id?: number
}

// Props
const props = defineProps<{
  markers: MARKERS[]
}>()

// Emits
const emit = defineEmits<{
  updateMarkers: [markers: MARKERS[]]
  locateToMarker: [marker: MARKERS]
  displayMarkers: []
}>()

// 响应式数据
const showMarkerListPopup = ref(false)
const showMarkerPopup = ref(false)
const currentMarker = ref<MARKERS>({
  name: '',
  description: '',
  address: '',
  lng: 0,
  lat: 0
})
const isEditingMarker = ref(false)
const editingMarkerIndex = ref(-1)

// 显示标点列表
const showMarkerList = () => {
  showMarkerListPopup.value = true
}

// 关闭标点列表
const closeMarkerList = () => {
  showMarkerListPopup.value = false
}

// 添加新标点
const addNewMarker = () => {
  currentMarker.value = {
    name: '',
    description: '',
    address: '',
    lng: 0,
    lat: 0
  }
  isEditingMarker.value = false
  showMarkerPopup.value = true
}

// 编辑标点
const editMarker = (index: number) => {
  const marker = props.markers[index]
  currentMarker.value = { ...marker }
  isEditingMarker.value = true
  editingMarkerIndex.value = index
  showMarkerPopup.value = true
}

defineExpose({
  editMarker
})

// 定位到标点
const locateToMarker = (marker: MARKERS) => {
  emit('locateToMarker', marker)
  closeMarkerList()
}

// 确认删除标点
const confirmDeleteMarker = (index: number) => {
  const marker = props.markers[index]

  // 使用 van-dialog 确认删除
  showConfirmDialog({
    title: '确认删除',
    message: `确定要删除标点"${marker.name}"吗？`,
  }).then(() => {
    deleteMarker(index)
  }).catch(() => {
    // 用户取消删除
  })
}

// 删除标点
const deleteMarker = async (index: number) => {
  try {
    const marker = props.markers[index]
    if (marker.id) {
      await api.locationdel({ id: marker.id })
    }

    const newMarkers = [...props.markers]
    newMarkers.splice(index, 1)
    emit('updateMarkers', newMarkers)
    emit('displayMarkers')
  } catch (error) {
    console.error('删除标点失败:', error)
  }
}

// 保存标点
const saveMarker = async () => {
  if (!currentMarker.value.name.trim()) {
    alert('请输入标点名称')
    return
  }

  try {
    // 调用API保存
    await api.locationsave(currentMarker.value)

    const newMarkers = [...props.markers]

    if (isEditingMarker.value) {
      // 编辑现有标点
      newMarkers[editingMarkerIndex.value] = { ...currentMarker.value }
    } else {
      // 添加新标点
      newMarkers.push({ ...currentMarker.value })
    }

    emit('updateMarkers', newMarkers)
    emit('displayMarkers')
    closeMarkerPopup()
  } catch (error) {
    console.error('保存标点失败:', error)
  }
}

// 关闭标点弹窗
const closeMarkerPopup = () => {
  showMarkerPopup.value = false
  currentMarker.value = {
    name: '',
    description: '',
    address: '',
    lng: 0,
    lat: 0
  }
  isEditingMarker.value = false
  editingMarkerIndex.value = -1
}
</script>

<style scoped lang="scss">
.marker-list-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.marker-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
}

.marker-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.marker-popup {
  .van-field {
    margin-bottom: 12px;
  }
}

:deep(.van-cell) {
  &:hover {
    background-color: #f5f5f5;
  }

  .van-cell__title {
    font-weight: 500;
    color: #333;
  }

  .van-cell__label {
    color: #666;
    font-size: 12px;
  }
}
</style>
