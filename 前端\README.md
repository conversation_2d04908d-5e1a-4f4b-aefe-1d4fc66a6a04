# Vue3 + Vant + Tailwind CSS 移动端应用

这是一个基于 Vue3 + Vite + Vant + Tailwind CSS 的现代化移动端应用模板，包含以下特性：

## 🚀 特性

- ✅ **Vue 3** - 使用最新的 Vue 3 Composition API
- ✅ **Vite** - 快速的构建工具
- ✅ **TypeScript** - 类型安全
- ✅ **Vant 4** - 轻量、可靠的移动端组件库
- ✅ **Tailwind CSS** - 原子化 CSS 框架，快速构建现代化界面
- ✅ **postcss-px-to-viewport** - 自动将 px 转换为 vw，实现移动端适配
- ✅ **@vant/touch-emulator** - 桌面端触摸事件模拟，方便开发调试
- ✅ **Vue Router** - 路由管理
- ✅ **Pinia** - 状态管理
- ✅ **ESLint** - 代码规范

## 📦 依赖说明

### 核心依赖
- `vue`: Vue 3 框架
- `vant`: Vant 4 移动端组件库
- `@vant/touch-emulator`: 桌面端触摸模拟器
- `vue-router`: 路由管理
- `pinia`: 状态管理

### 开发依赖
- `vite`: 构建工具
- `typescript`: TypeScript 支持
- `tailwindcss`: 原子化 CSS 框架
- `postcss`: CSS 后处理器
- `autoprefixer`: 自动添加浏览器前缀
- `postcss-px-to-viewport`: px 转 vw 插件
- `unplugin-vue-components`: Vant 组件按需引入

## 🛠️ 配置说明

### 1. Vant 组件按需引入
通过 `unplugin-vue-components` 实现 Vant 组件的按需引入，无需手动导入组件。

### 2. 移动端适配
使用 `postcss-px-to-viewport` 插件，自动将 CSS 中的 px 单位转换为 vw，实现移动端适配。

配置参数（postcss.config.js）：
- `viewportWidth: 375` - 设计稿宽度
- `unitPrecision: 3` - 转换精度
- `viewportUnit: 'vw'` - 转换单位

### 3. Tailwind CSS 集成
集成了 Tailwind CSS 原子化 CSS 框架，与 Vant 组件库完美兼容。

配置特点：
- 禁用了 `preflight` 避免与 Vant 样式冲突
- 自定义了移动端友好的断点和颜色
- 排除了 Vant 组件的 px 转换
- 提供了常用的工具类和组件样式

### 4. 桌面端适配
引入 `@vant/touch-emulator` 在桌面端模拟触摸事件，方便开发调试。

## 🚀 快速开始

### 安装依赖

```sh
npm install
```

### 开发环境

```sh
npm run dev
```

### 构建生产版本

```sh
npm run build
```

### 代码检查

```sh
npm run lint
```

## 📱 使用示例

项目已经配置好了 Vant 组件的使用示例，可以直接在组件中使用：

```vue
<template>
  <van-button type="primary">按钮</van-button>
  <van-cell title="单元格" value="内容" />
</template>
```

无需手动导入，组件会自动按需引入。

## 🎯 开发建议

1. 设计稿建议使用 375px 宽度，这样 px 转 vw 的效果最佳
2. 对于不需要转换的样式，可以使用 `.ignore` 类名
3. 在桌面端开发时，可以使用浏览器的移动端模拟器查看效果
4. 建议使用 VSCode + Volar 插件获得最佳开发体验
