// 用户登录 注册相关
import { defineStore } from "pinia";
import { ref } from "vue";
import * as api from "@/api/index";
import { showToast } from "vant";

export const useUserStore = defineStore(
  "user",
  () => {
    const userInfo = ref<{ id: number; name: string; user: string } | null>(null);

    const login = async (username: string, password: string) => {
      const response = await api.login({
        user: username.trim(),
        pwd: password.trim(),
      });
      if (response.success) {
        userInfo.value = response.data;
        showToast("登录成功");
        return true;
      }
      return false;
    };

    const logout = () => {
      userInfo.value = null;
      showToast("退出成功");
    };

    const registry = async (username: string, password: string, name: string) => {
      // 调用注册API
      const response = await api.register({
        user: username.trim(),
        pwd: password.trim(),
        name: name.trim(),
      });
      if (response.success) {
        showToast("注册成功");
        return true;
      }
      return false;
    };

    return { userInfo, login, logout, registry };
  },
  {
    persist: {
      key: "user-store",
      storage: sessionStorage, // 或 localStorage
      pick: ["userInfo"], // 只持久化 userInfo
    },
  }
);
